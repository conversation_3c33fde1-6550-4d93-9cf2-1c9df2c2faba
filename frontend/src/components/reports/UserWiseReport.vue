<template>
  <div class="bg-gray-900/30 p-6 rounded-lg">
    <div class="space-y-6">
      <div v-if="!selectedUser" class="flex justify-between items-center">
        <h3 class="text-lg font-semibold text-white">All Users</h3>
        <Button
          variant="generalAction"
          size="backButton"
          :disabled="usersLoading"
          class="bg-purple-600 hover:bg-purple-700"
          @click="fetchAllUsers"
        >
          {{ usersLoading ? "Loading..." : "Refresh Users" }}
        </Button>
      </div>

      <!-- Error/Success message -->
      <Alert v-if="message" :variant="isSuccess ? 'success' : 'error'">
        <AlertDescription>{{ message }}</AlertDescription>
      </Alert>

      <!-- User List Table -->
      <div v-if="!selectedUser" class="overflow-x-auto">
        <table class="w-full border-collapse">
          <thead>
            <tr class="bg-gray-800/50 border-b border-gray-700">
              <th
                class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
              >
                User ID
              </th>
              <th
                class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
              >
                Name
              </th>
              <th
                class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
              >
                Email
              </th>
              <th
                class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
              >
                Assessments
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-700">
            <tr
              v-for="user in users"
              :key="user.id"
              class="hover:bg-gray-700/30 transition-colors duration-150 cursor-pointer"
              title="Click to view user details"
              @click="selectUser(user.id)"
            >
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-200">
                {{ user.external_id || user.id || "Unknown ID" }}
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-200">
                {{
                  user.display_name ||
                  user.name ||
                  user.external_id ||
                  "Unknown Name"
                }}
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-200">
                {{ user.email || "N/A" }}
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-200">
                <span
                  :class="[
                    'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full',
                    user.session_count > 0 || user.assessments > 0
                      ? 'bg-green-800/50 text-green-100'
                      : 'bg-gray-800/50 text-gray-300',
                  ]"
                >
                  {{ user.session_count || user.assessments || 0 }}
                </span>
              </td>
            </tr>
            <tr v-if="users.length === 0" class="cursor-default">
              <td colspan="4" class="px-4 py-8 text-center text-gray-400">
                <div v-if="usersLoading">Loading users...</div>
                <div v-else>
                  No users found. Click "Refresh Users" to load users.
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- User Assessment Details -->
      <div v-if="selectedUser" class="space-y-6">
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold text-white">
            User Details:
            {{
              userDetails?.display_name ||
              userDetails?.external_id ||
              "Loading..."
            }}
          </h3>
          <Button variant="userBack" size="backButton" @click="backToUserList">
            <span class="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M10 19l-7-7m0 0l7-7m-7 7h18"
                />
              </svg>
              Back to User List
            </span>
          </Button>
        </div>

        <div class="grid grid-cols-1 gap-4 mb-6">
          <div class="bg-gray-800/30 rounded-lg border border-purple-500/30">
            <!-- Spider Chart for Skill Performance -->
            <div class="bg-gray-800/30 rounded-lg p-5">
              <div class="flex justify-between items-center mb-2">
                <h4 class="text-md font-medium text-white">
                  Skill Performance Overview
                </h4>
                <button
                  class="text-xs text-purple-400 hover:text-purple-300 flex items-center"
                  @click="spiderChartModal.open()"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                    />
                  </svg>
                  View Full Size
                </button>
              </div>
              <div
                class="h-72 cursor-pointer relative group"
                title="Click to view in full size"
                @click="spiderChartModal.open()"
              >
                <div
                  class="absolute inset-0 bg-purple-500/0 group-hover:bg-purple-500/10 transition-colors duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100 z-10"
                />
                <UserSkillsSpiderChart
                  :skills="skillPerformanceData"
                  color="#8b5cf6"
                  background-color="rgba(139, 92, 246, 0.2)"
                  :max-value="100"
                  :max-skills="6"
                />
              </div>
            </div>
          </div>

          <div
            class="bg-gray-800/30 rounded-lg border border-purple-500/30 shadow-lg hover:shadow-purple-500/10 transition-all duration-300"
          >
            <!-- Stats Summary -->
            <div class="rounded-lg p-6">
              <!-- Main Focus Alert - Prominent at the top -->
              <div
                v-if="getWorstSkill()?.accuracy_percentage < 60"
                class="bg-gradient-to-r from-red-900/40 to-red-700/20 border-l-4 border-red-500 rounded-lg p-4 mb-6 flex items-start"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-6 w-6 text-red-400 mr-3 flex-shrink-0"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                  />
                </svg>
                <div>
                  <h3 class="text-lg font-bold text-white">
                    Focus Required: {{ getWorstSkill()?.skill_name }}
                  </h3>
                  <p class="text-red-200 mt-1">
                    Your performance in
                    <span class="font-bold text-white">{{
                      getWorstSkill()?.skill_name
                    }}</span>
                    is at
                    <span class="font-bold text-red-300"
                      >{{ getWorstSkill()?.accuracy_percentage }}%</span
                    >, which requires immediate attention. We recommend
                    prioritizing this skill in your learning plan.
                  </p>
                  <div class="mt-3 flex space-x-2">
                    <div
                      class="bg-red-900/40 px-3 py-1 rounded-full text-xs text-red-200 font-medium"
                    >
                      Critical Skill Gap
                    </div>
                    <div
                      class="bg-red-900/40 px-3 py-1 rounded-full text-xs text-red-200 font-medium"
                    >
                      High Priority
                    </div>
                  </div>
                </div>
              </div>

              <h4
                class="text-lg font-semibold text-white mb-4 flex items-center"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2 text-purple-400"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zm6-4a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zm6-3a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"
                  />
                </svg>
                Performance Summary
              </h4>

              <!-- Main Stats -->
              <div class="grid grid-cols-2 gap-6 mb-6">
                <div
                  class="bg-gray-800/50 rounded-lg p-4 border border-purple-500/20 transform hover:scale-105 transition-transform duration-300"
                >
                  <p
                    class="text-sm text-purple-300 uppercase tracking-wider mb-1"
                  >
                    Overall Accuracy
                  </p>
                  <div class="flex items-end">
                    <p class="text-2xl font-bold text-white">
                      {{ getOverallAccuracy() }}%
                    </p>
                    <div
                      class="ml-2 h-6 w-16 bg-gray-700 rounded-full overflow-hidden"
                    >
                      <div
                        class="h-full bg-gradient-to-r from-purple-500 to-purple-300"
                        :style="`width: ${Math.min(getOverallAccuracy(), 100)}%`"
                      />
                    </div>
                  </div>
                </div>

                <div
                  class="bg-gray-800/50 rounded-lg p-4 border border-purple-500/20 transform hover:scale-105 transition-transform duration-300"
                >
                  <p
                    class="text-sm text-purple-300 uppercase tracking-wider mb-1"
                  >
                    Total Questions
                  </p>
                  <p class="text-2xl font-bold text-white">
                    {{ getTotalQuestions() }}
                  </p>
                </div>
              </div>

              <!-- Skill Performance Section -->
              <h6
                class="text-sm font-medium text-purple-300 uppercase tracking-wider mb-3 flex items-center"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4 mr-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M13 10V3L4 14h7v7l9-11h-7z"
                  />
                </svg>
                Skill Performance
              </h6>

              <div class="grid grid-cols-2 gap-6 mb-6">
                <div
                  class="bg-gray-800/50 rounded-lg p-4 border border-green-500/20 transform hover:translate-y-[-2px] transition-transform duration-300"
                >
                  <div class="flex items-center mb-2">
                    <div class="w-2 h-2 rounded-full bg-green-400 mr-2" />
                    <p class="text-sm text-green-300 uppercase tracking-wider">
                      Best Skill
                    </p>
                  </div>
                  <p class="text-lg font-bold text-white mb-1 truncate">
                    {{ getBestSkill()?.skill_name || "N/A" }}
                  </p>
                  <div class="flex items-center">
                    <div class="w-full bg-gray-700 rounded-full h-2.5 mr-2">
                      <div
                        class="bg-gradient-to-r from-green-500 to-green-300 h-2.5 rounded-full"
                        :style="`width: ${getBestSkill()?.accuracy_percentage || 0}%`"
                      />
                    </div>
                    <p
                      class="text-sm font-medium text-green-400 whitespace-nowrap"
                    >
                      {{ getBestSkill()?.accuracy_percentage || 0 }}%
                    </p>
                  </div>
                </div>

                <div
                  class="bg-gray-800/50 rounded-lg p-4 border border-red-500/20 transform hover:translate-y-[-2px] transition-transform duration-300"
                >
                  <div class="flex items-center mb-2">
                    <div class="w-2 h-2 rounded-full bg-red-400 mr-2" />
                    <p class="text-sm text-red-300 uppercase tracking-wider">
                      Needs Improvement
                    </p>
                  </div>
                  <p class="text-lg font-bold text-white mb-1 truncate">
                    {{ getWorstSkill()?.skill_name || "N/A" }}
                  </p>
                  <div class="flex items-center">
                    <div class="w-full bg-gray-700 rounded-full h-2.5 mr-2">
                      <div
                        class="bg-gradient-to-r from-red-500 to-red-300 h-2.5 rounded-full"
                        :style="`width: ${getWorstSkill()?.accuracy_percentage || 0}%`"
                      />
                    </div>
                    <p
                      class="text-sm font-medium text-red-400 whitespace-nowrap"
                    >
                      {{ getWorstSkill()?.accuracy_percentage || 0 }}%
                    </p>
                  </div>
                </div>
              </div>

              <!-- Assessment Stats Section -->
              <h6
                class="text-sm font-medium text-purple-300 uppercase tracking-wider mb-3 flex items-center"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4 mr-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                  />
                </svg>
                Assessment Overview
              </h6>

              <div class="grid grid-cols-3 gap-4">
                <div
                  class="bg-gray-800/50 rounded-lg p-3 border border-purple-500/20 text-center"
                >
                  <p
                    class="text-xs text-purple-300 uppercase tracking-wider mb-1"
                  >
                    Total
                  </p>
                  <p class="text-xl font-bold text-white">
                    {{ userAssessments.length }}
                  </p>
                </div>

                <div
                  class="bg-gray-800/50 rounded-lg p-3 border border-green-500/20 text-center"
                >
                  <p
                    class="text-xs text-green-300 uppercase tracking-wider mb-1"
                  >
                    Completed
                  </p>
                  <p class="text-xl font-bold text-white">
                    {{
                      userAssessments.filter((a) => a.status === "completed")
                        .length
                    }}
                  </p>
                </div>

                <div
                  class="bg-gray-800/50 rounded-lg p-3 border border-yellow-500/20 text-center"
                >
                  <p
                    class="text-xs text-yellow-300 uppercase tracking-wider mb-1"
                  >
                    Pending
                  </p>
                  <p class="text-xl font-bold text-white">
                    {{
                      userAssessments.filter((a) => a.status === "pending")
                        .length
                    }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <h4 class="text-md font-medium text-white mt-6 mb-3">
          Assessment History
        </h4>
        <div class="overflow-x-auto bg-gray-800/30 rounded-lg p-4">
          <table class="w-full border-collapse">
            <thead>
              <tr class="bg-gray-700/70 border-b border-gray-700">
                <th
                  class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Assessment
                </th>
                <th
                  class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Mode
                </th>
                <th
                  class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Status
                </th>
                <th
                  class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Score
                </th>
                <th
                  class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Easy
                </th>
                <th
                  class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Intermediate
                </th>
                <th
                  class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                >
                  Advanced
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-700">
              <tr
                v-for="assessment in userAssessments"
                :key="assessment.session_id"
                class="hover:bg-gray-700/30 transition-colors duration-150"
              >
                <td class="px-4 py-3 text-sm text-gray-200">
                  {{ assessment.assessment_name }}
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-200">
                  {{ formatDate(assessment.mode) }}
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm">
                  <span
                    :class="[
                      'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full',
                      assessment.status === 'completed'
                        ? 'bg-green-800/50 text-green-100'
                        : 'bg-yellow-800/50 text-yellow-100',
                    ]"
                  >
                    {{
                      assessment.status === "completed"
                        ? "Completed"
                        : assessment.status === "pending"
                          ? "Pending"
                          : assessment.status
                    }}
                  </span>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm">
                  <div
                    v-if="assessment.status === 'completed'"
                    class="flex items-center"
                  >
                    <span class="text-gray-200">{{
                      assessment.score || 0
                    }}</span>
                  </div>
                  <span v-else class="text-gray-400">-</span>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm">
                  <span class="text-gray-200">{{
                    assessment.easy_count || 0
                  }}</span>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm">
                  <span class="text-gray-200">{{
                    assessment.intermediate_count || 0
                  }}</span>
                </td>
                <td class="px-4 py-3 whitespace-nowrap text-sm">
                  <span class="text-gray-200">{{
                    assessment.advanced_count || 0
                  }}</span>
                </td>
              </tr>
              <tr v-if="userAssessments.length === 0">
                <td colspan="7" class="px-4 py-8 text-center text-gray-400">
                  <div v-if="userAssessmentsLoading">
                    Loading assessment history...
                  </div>
                  <div v-else>No assessments found for this user.</div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Skill History Section -->
        <div v-if="skillPerformanceData.length > 0" class="mt-8 space-y-4">
          <h4 class="text-md font-medium text-white mb-3">
            Skill Performance Data
          </h4>

          <!-- Comprehensive Skill Data Table -->
          <div class="overflow-x-auto bg-gray-800/30 rounded-lg p-4">
            <div class="overflow-x-auto">
              <table class="w-full text-sm text-left text-gray-300">
                <thead class="text-xs uppercase bg-gray-700/70 text-gray-300">
                  <tr>
                    <th scope="col" class="px-3 py-2 whitespace-nowrap">
                      Skill Name
                    </th>
                    <th scope="col" class="px-3 py-2 whitespace-nowrap">
                      Questions
                    </th>
                    <th scope="col" class="px-3 py-2 whitespace-nowrap">
                      Correct
                    </th>
                    <th scope="col" class="px-3 py-2 whitespace-nowrap">
                      Accuracy %
                    </th>
                    <th scope="col" class="px-3 py-2 whitespace-nowrap">
                      Total Score
                    </th>
                    <th scope="col" class="px-3 py-2 whitespace-nowrap">
                      Avg Score
                    </th>
                    <th scope="col" class="px-3 py-2 whitespace-nowrap">
                      Easy Correct
                    </th>
                    <th scope="col" class="px-3 py-2 whitespace-nowrap">
                      Easy Incorrect
                    </th>
                    <th scope="col" class="px-3 py-2 whitespace-nowrap">
                      Int. Correct
                    </th>
                    <th scope="col" class="px-3 py-2 whitespace-nowrap">
                      Int. Incorrect
                    </th>
                    <th scope="col" class="px-3 py-2 whitespace-nowrap">
                      Adv. Correct
                    </th>
                    <th scope="col" class="px-3 py-2 whitespace-nowrap">
                      Adv. Incorrect
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="skill in skillPerformanceData"
                    :key="skill.skill_name"
                    class="border-b border-gray-700/50 hover:bg-gray-700/30 transition-colors duration-150"
                  >
                    <td class="px-3 py-2 font-medium text-white">
                      {{ skill.skill_name }}
                    </td>
                    <td class="px-3 py-2">
                      {{ skill.total_questions_answered }}
                    </td>
                    <td class="px-3 py-2">
                      {{ skill.correct_answers }}
                    </td>
                    <td class="px-3 py-2">
                      <span
                        :class="[
                          'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full',
                          skill.accuracy_percentage >= 80
                            ? 'bg-green-800/50 text-green-100'
                            : skill.accuracy_percentage >= 60
                              ? 'bg-yellow-800/50 text-yellow-100'
                              : 'bg-red-800/50 text-red-100',
                        ]"
                      >
                        {{ skill.accuracy_percentage }}%
                      </span>
                    </td>
                    <td class="px-3 py-2">
                      {{ skill.total_score }}
                    </td>
                    <td class="px-3 py-2">
                      {{ skill.avg_score }}
                    </td>
                    <td class="px-3 py-2 text-green-400">
                      {{ skill.easy_correct || 0 }}
                    </td>
                    <td class="px-3 py-2 text-red-400">
                      {{ skill.easy_incorrect || 0 }}
                    </td>
                    <td class="px-3 py-2 text-green-400">
                      {{ skill.intermediate_correct || 0 }}
                    </td>
                    <td class="px-3 py-2 text-red-400">
                      {{ skill.intermediate_incorrect || 0 }}
                    </td>
                    <td class="px-3 py-2 text-green-400">
                      {{ skill.advanced_correct || 0 }}
                    </td>
                    <td class="px-3 py-2 text-red-400">
                      {{ skill.advanced_incorrect || 0 }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Spider Chart Modal -->
  <Dialog v-model="showSpiderChartModal" title="Skill Performance Overview">
    <div class="bg-gray-900 rounded-lg w-full mx-auto">
      <div class="h-96 w-full">
        <UserSkillsSpiderChart
          v-if="skillPerformanceData.length > 0"
          :skills="skillPerformanceData"
          color="#8b5cf6"
          background-color="rgba(139, 92, 246, 0.2)"
          :max-value="100"
          :max-skills="6"
        />
        <div v-else class="h-full w-full flex items-center justify-center">
          <p class="text-gray-400">No skill performance data available</p>
        </div>
      </div>
    </div>
  </Dialog>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Dialog } from "@/components/ui/dialog";
import { getErrorMessage, logError } from "@/utils/errorHandling";
import { useMessageHandler } from "@/utils/messageHandler";
import { useModal, useLoadingState } from "@/composables";
import {
  extractResponseData,
  extractErrorInfo,
} from "@/utils/apiResponseHandler";
import { api } from "@/services/api";
import { UserSkillsSpiderChart } from "../charts";
import { error } from "@/utils/logger";

const { message, isSuccess, setSuccessMessage, setErrorMessage, clearMessage } =
  useMessageHandler();

// This will be populated with user skill performance data
const skillPerformanceData = ref([]);

// Helper functions for skill performance data
const getBestSkill = () => {
  if (!skillPerformanceData.value || skillPerformanceData.value.length === 0) {
    return null;
  }

  return [...skillPerformanceData.value].sort(
    (a, b) => (b.accuracy_percentage || 0) - (a.accuracy_percentage || 0),
  )[0];
};

const getWorstSkill = () => {
  if (!skillPerformanceData.value || skillPerformanceData.value.length === 0) {
    return null;
  }

  return [...skillPerformanceData.value].sort(
    (a, b) => (a.accuracy_percentage || 0) - (b.accuracy_percentage || 0),
  )[0];
};

const getOverallAccuracy = () => {
  if (!skillPerformanceData.value || skillPerformanceData.value.length === 0) {
    return 0;
  }

  const totalAccuracy = skillPerformanceData.value.reduce(
    (sum, skill) => sum + (skill.accuracy_percentage || 0),
    0,
  );

  return Math.round(totalAccuracy / skillPerformanceData.value.length);
};

const getTotalQuestions = () => {
  if (!skillPerformanceData.value || skillPerformanceData.value.length === 0) {
    return 0;
  }

  return skillPerformanceData.value.reduce(
    (sum, skill) => sum + (skill.total_questions_answered || 0),
    0,
  );
};

// Modal state using composable
const spiderChartModal = useModal();

// Loading states using composables
const usersLoadingState = useLoadingState();
const userAssessmentsLoadingState = useLoadingState();

// All Users Tab Data
const users = ref([]);
const selectedUser = ref(null);
const userDetails = ref(null);
const userAssessments = ref([]);

// Aliases for backward compatibility
const usersLoading = usersLoadingState.isLoading;
const userAssessmentsLoading = userAssessmentsLoadingState.isLoading;

// Format date for display
const formatDate = (dateString) => {
  if (!dateString) return "N/A";

  // Handle special case for "dynamic" mode
  if (dateString === "dynamic") {
    return "Dynamic";
  }

  try {
    const date = new Date(dateString);

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return dateString; // Return as is without logging
    }

    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  } catch (error) {
    logError(error, "formatDate");
    return dateString; // Return as is without logging
  }
};

// Fetch all users
const fetchAllUsers = async () => {
  usersLoading.value = true;
  clearMessage();
  users.value = []; // Clear existing users

  try {
    const response = await api.admin.getUsers();
    const data = extractResponseData(response);

    // Check if the response has the expected structure
    if (data) {
      if (data.users && Array.isArray(data.users)) {
        users.value = data.users;

        if (users.value.length === 0) {
          setErrorMessage("No users found in the database.");
        } else {
          setSuccessMessage(`Successfully loaded ${users.value.length} users.`);
        }
      } else {
        setErrorMessage(
          "Invalid response format from server. Expected users array.",
        );

        // Try to extract users from a different format if possible
        if (typeof data === "object") {
          const possibleUsers = Object.values(data).find((val) =>
            Array.isArray(val),
          );
          if (possibleUsers && possibleUsers.length > 0) {
            users.value = possibleUsers;
            setSuccessMessage(
              `Successfully loaded ${users.value.length} users.`,
            );
          }
        }
      }
    } else {
      setErrorMessage("Invalid response from server");
    }
  } catch (error) {
    logError(error, "fetchAllUsers");
    const errorInfo = extractErrorInfo(error);
    setErrorMessage(errorInfo.message || "Failed to fetch users");
    users.value = [];
  } finally {
    usersLoading.value = false;
  }
};

// Select a user to view details
const selectUser = async (userId) => {
  selectedUser.value = userId;
  userDetails.value = null;
  userAssessments.value = [];
  skillPerformanceData.value = [];
  userAssessmentsLoading.value = true;
  clearMessage();

  try {
    // Find the user in the local array first
    const localUser = users.value.find((u) => u.id === userId);

    if (!localUser) {
      setErrorMessage("User not found in local data");
      selectedUser.value = null;
      return;
    }

    try {
      const response = await api.admin.getUserAssessments(userId);
      const data = extractResponseData(response);

      if (data) {
        // Set user details
        userDetails.value = {
          id: data.user?.id || localUser.id,
          external_id:
            data.user?.external_id || localUser.external_id || localUser.id,
          email: data.user?.email || localUser.email || "N/A",
          display_name:
            data.user?.display_name ||
            localUser.display_name ||
            localUser.name ||
            localUser.external_id ||
            "Unknown User",
        };

        // Set user assessments
        if (data.assessments && Array.isArray(data.assessments)) {
          // Map the assessments data with default values for missing properties
          userAssessments.value = data.assessments.map((assessment) => {
            return {
              session_id: assessment.session_id || Date.now(),
              assessment_name:
                assessment.assessment_name || "Unknown Assessment",
              mode:
                assessment.mode ||
                assessment.session_created ||
                new Date().toISOString(),
              status: assessment.status || "unknown",
              score: parseInt(assessment.score || 0),
              easy_count: parseInt(assessment.easy_count || 0),
              intermediate_count: parseInt(assessment.intermediate_count || 0),
              advanced_count: parseInt(assessment.advanced_count || 0),
              // Include any other properties that might be used in the template
              session_created: assessment.session_created,
              session_completed: assessment.session_completed,
              assessment_id: assessment.assessment_id,
              assessment_description: assessment.assessment_description,
              max_score: parseInt(assessment.max_score || 0),
              percentage_score: parseInt(assessment.percentage_score || 0),
            };
          });
        } else {
          userAssessments.value = [];
        }

        // Fetch skill performance data from the separate endpoint
        try {
          const skillResponse = await api.admin.getUserSkillPerformance(userId);
          const skillData = extractResponseData(skillResponse);

          if (skillData && Array.isArray(skillData) && skillData.length > 0) {
            skillPerformanceData.value = skillData.map((skill) => ({
              skill_name: skill.skill_name || "Unknown Skill",
              total_questions_answered: parseInt(
                skill.total_questions_answered || 0,
              ),
              correct_answers: parseInt(skill.correct_answers || 0),
              accuracy_percentage: parseInt(skill.accuracy_percentage || 0),
              total_score: parseInt(skill.total_score || 0),
              avg_score: parseFloat(skill.avg_score || 0),
              easy_correct: parseInt(skill.easy_correct || 0),
              easy_incorrect: parseInt(skill.easy_incorrect || 0),
              intermediate_correct: parseInt(skill.intermediate_correct || 0),
              intermediate_incorrect: parseInt(
                skill.intermediate_incorrect || 0,
              ),
              advanced_correct: parseInt(skill.advanced_correct || 0),
              advanced_incorrect: parseInt(skill.advanced_incorrect || 0),
            }));
          } else {
            skillPerformanceData.value = [];
          }
        } catch (skillError) {
          logError(skillError, "getUserSkillPerformance");
          const errorInfo = extractErrorInfo(skillError);
          setErrorMessage(
            errorInfo.message || "Failed to fetch skill performance data",
          );

          // Fallback to empty data if API fails
          skillPerformanceData.value = [];
        }
      } else {
        setErrorMessage("Invalid response format from server");

        // Use local user data as fallback
        userDetails.value = {
          id: localUser.id,
          external_id: localUser.external_id || localUser.id,
          email: localUser.email || "N/A",
          display_name:
            localUser.display_name ||
            localUser.name ||
            localUser.external_id ||
            "Unknown User",
        };

        // Create a mock assessment as fallback
        userAssessments.value = [
          {
            session_id: Date.now(),
            session_code: "FALLBACK",
            session_created: new Date().toISOString(),
            session_completed: null,
            assessment_id: 1,
            assessment_name: "No Assessment Data Available",
            assessment_description:
              "Could not retrieve assessment data from server",
            score: 0,
            max_score: 0,
            percentage_score: 0,
            status: "in_progress",
          },
        ];
      }
    } catch (apiError) {
      error("API error:", { error: apiError });
      setErrorMessage(
        "Error fetching user assessment details. Using fallback data.",
      );

      // Use local user data as fallback
      userDetails.value = {
        id: localUser.id,
        external_id: localUser.external_id || localUser.id,
        email: localUser.email || "N/A",
        display_name:
          localUser.display_name ||
          localUser.name ||
          localUser.external_id ||
          "Unknown User",
      };

      // Create a mock assessment as fallback
      userAssessments.value = [
        {
          session_id: Date.now(),
          session_code: "ERROR",
          session_created: new Date().toISOString(),
          session_completed: null,
          assessment_id: 1,
          assessment_name: "Error Retrieving Assessments",
          assessment_description:
            "An error occurred while retrieving assessment data",
          score: 0,
          max_score: 0,
          percentage_score: 0,
          status: "in_progress",
        },
      ];
    }
  } catch (selectError) {
    error("Error selecting user:", { error: selectError });
    logError(selectError, "selectUser");
    setErrorMessage(
      getErrorMessage(selectError, "Failed to fetch user assessment details"),
    );

    // Create fallback user details
    userDetails.value = {
      id: userId,
      external_id: "Unknown",
      email: "N/A",
      display_name: "Unknown User",
    };
  } finally {
    userAssessmentsLoading.value = false;
  }
};

// Go back to user list
const backToUserList = () => {
  selectedUser.value = null;
  userDetails.value = null;
  userAssessments.value = [];
  skillPerformanceData.value = [];
};

// Initialize component
onMounted(() => {
  fetchAllUsers();
});
</script>
